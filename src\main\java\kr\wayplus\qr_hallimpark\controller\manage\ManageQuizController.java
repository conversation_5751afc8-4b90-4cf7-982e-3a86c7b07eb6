package kr.wayplus.qr_hallimpark.controller.manage;

import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Quiz;
import kr.wayplus.qr_hallimpark.model.QuizCategory;
import kr.wayplus.qr_hallimpark.service.QuizService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * 문제 관리 컨트롤러
 * - 관리자 문제 관리 페이지 처리
 * - 문제 CRUD 및 검색 기능 제공
 */
@Slf4j
@Controller
@RequestMapping("/manage/quiz")
@RequiredArgsConstructor
public class ManageQuizController {
    
    private final QuizService quizService;

    /**
     * 문제 관리 메인 페이지
     */
    @GetMapping
    public ModelAndView quizList(@ModelAttribute AdminListSearch searchCondition) {
        log.debug("Quiz management page requested with search condition: {}", searchCondition);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/quiz/list");

        try {
            // 기본 검색 조건 설정
            if (searchCondition.getPage() == null || searchCondition.getPage() < 1) {
                searchCondition = quizService.createDefaultSearchCondition();
            }

            // 초기 데이터 조회
            AdminListResponse<Quiz> listResponse = quizService.findListWithConditions(searchCondition);

            // 최상위 카테고리 목록 조회
            List<QuizCategory> rootCategories = quizService.findRootCategoriesForQuizManagement();

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "문제 관리");
            modelAndView.addObject("pageDescription", "문제를 관리합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("searchCondition", searchCondition);
            modelAndView.addObject("listResponse", listResponse);
            modelAndView.addObject("rootCategories", rootCategories);

        } catch (Exception e) {
            log.error("Error loading quiz list: {}", e.getMessage(), e);
            modelAndView.addObject("errorMessage", "문제 목록을 불러오는 중 오류가 발생했습니다.");
        }

        return modelAndView;
    }

    /**
     * 문제 등록 페이지
     */
    @GetMapping("/new")
    public ModelAndView quizForm() {
        log.debug("Quiz form page requested");

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        ModelAndView modelAndView = new ModelAndView("manage/quiz/form");

        try {
            // 최상위 카테고리 목록 조회
            List<QuizCategory> rootCategories = quizService.findRootCategoriesForQuizManagement();

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "문제 등록");
            modelAndView.addObject("pageDescription", "새로운 문제를 등록합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("isEdit", false);
            modelAndView.addObject("rootCategories", rootCategories);

            // 빈 Quiz 객체 생성 (기본값 설정)
            Quiz quiz = Quiz.builder()
                    .status("ACTIVE")
                    .difficultyLevel(1)
                    .deleteYn("N")
                    .build();
            modelAndView.addObject("quiz", quiz);

        } catch (Exception e) {
            log.error("Error loading quiz form: {}", e.getMessage(), e);
            modelAndView.addObject("errorMessage", "문제 등록 페이지를 불러오는 중 오류가 발생했습니다.");
        }

        return modelAndView;
    }

    /**
     * 문제 수정 페이지 (향후 구현)
     */
    @GetMapping("/{quizId}/edit")
    public ModelAndView quizEditForm(@PathVariable("quizId") Long quizId) {
        log.debug("Quiz edit form page requested. Quiz ID: {}", quizId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();

        try {
            // 기존 문제 정보 조회 (다국어 콘텐츠 포함)
            Quiz quiz = quizService.findQuizByIdWithContents(quizId);

            // 최상위 카테고리 목록 조회
            List<QuizCategory> rootCategories = quizService.findRootCategoriesForQuizManagement();

            ModelAndView modelAndView = new ModelAndView("manage/quiz/form");

            // 페이지 메타 정보 설정
            modelAndView.addObject("pageTitle", "문제 수정");
            modelAndView.addObject("pageDescription", "문제 정보를 수정합니다.");
            modelAndView.addObject("username", auth.getName());
            modelAndView.addObject("quiz", quiz);
            modelAndView.addObject("isEdit", true);
            modelAndView.addObject("quizId", quizId);
            modelAndView.addObject("rootCategories", rootCategories);

            return modelAndView;

        } catch (IllegalArgumentException e) {
            log.warn("Invalid quiz ID: {}", e.getMessage());
            return new ModelAndView("redirect:/manage/quiz?error=invalid_quiz");
        } catch (Exception e) {
            log.error("Error loading quiz edit form: {}", e.getMessage(), e);
            return new ModelAndView("redirect:/manage/quiz?error=load_failed");
        }
    }

    /**
     * 문제 검색 (AJAX)
     */
    @PostMapping("/search")
    @ResponseBody
    public AdminListResponse<Quiz> searchQuizzes(@RequestBody AdminListSearch searchCondition) {
        log.debug("Quiz search requested with condition: {}", searchCondition);

        try {
            log.debug("Received search condition - selectedSearchField: {}, searchFields: {}, searchKeyword: {}",
                        searchCondition.getSelectedSearchField(),
                        searchCondition.getSearchFields(),
                        searchCondition.getSearchKeyword());

            // JavaScript에서 이미 searchFields를 설정해서 보내므로 별도 처리 불필요
            // 하지만 안전을 위해 기본값 설정
            if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
                searchCondition.setSearchFields(Arrays.asList("question", "category_name"));
            }

            return quizService.findListWithConditions(searchCondition);

        } catch (Exception e) {
            log.error("Error searching quizzes: {}", e.getMessage(), e);
            return AdminListResponse.error("문제 검색 중 오류가 발생했습니다: " + e.getMessage(), "SEARCH_ERROR");
        }
    }

    /**
     * 특정 카테고리의 문제 목록 조회 (AJAX)
     */
    @GetMapping("/category/{categoryId}")
    @ResponseBody
    public HashMap<String, Object> getQuizzesByCategory(@PathVariable("categoryId") Long categoryId) {
        log.debug("Getting quizzes by category ID: {}", categoryId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<Quiz> quizzes = quizService.findQuizzesWithChildCategories(categoryId);

            response.put("success", true);
            response.put("data", quizzes);
            response.put("message", "카테고리별 문제 목록을 성공적으로 조회했습니다.");

        } catch (Exception e) {
            log.error("Error getting quizzes by category: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "카테고리별 문제 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 하위 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/category/{parentId}/children")
    @ResponseBody
    public HashMap<String, Object> getChildCategories(@PathVariable("parentId") Long parentId) {
        log.debug("Getting child categories for parent ID: {}", parentId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> childCategories = quizService.findChildCategories(parentId);
            response.put("success", true);
            response.put("data", childCategories);
            response.put("message", "하위 카테고리 목록을 성공적으로 조회했습니다.");

        } catch (Exception e) {
            log.error("Error getting child categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "하위 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 문제 상세 조회 (AJAX)
     */
    @GetMapping("/{quizId}/detail")
    @ResponseBody
    public HashMap<String, Object> getQuiz(@PathVariable("quizId") Long quizId) {
        log.debug("Getting quiz. ID: {}", quizId);

        HashMap<String, Object> response = new HashMap<>();

        try {
            Quiz quiz = quizService.findQuizByIdWithContents(quizId);

            response.put("success", true);
            response.put("data", quiz);

        } catch (IllegalArgumentException e) {
            log.warn("Invalid quiz ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error getting quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 최상위 카테고리 목록 조회 (AJAX)
     */
    @GetMapping("/root-categories")
    @ResponseBody
    public HashMap<String, Object> getRootCategories() {
        log.debug("Getting root categories for quiz management");

        HashMap<String, Object> response = new HashMap<>();

        try {
            List<QuizCategory> rootCategories = quizService.findRootCategoriesForQuizManagement();

            response.put("success", true);
            response.put("data", rootCategories);

        } catch (Exception e) {
            log.error("Error getting root categories: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "최상위 카테고리 목록 조회 중 오류가 발생했습니다.");
        }

        return response;
    }

    // ========== 향후 구현할 CRUD 메서드들 ==========

    /**
     * 문제 등록 처리 (AJAX) - 향후 구현
     */
    @PostMapping("/add")
    @ResponseBody
    public HashMap<String, Object> createQuiz(@RequestBody Quiz quiz) {
        log.debug("Creating quiz: {}", quiz);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 생성자 정보 설정
            quiz.setCreateId(auth.getName());

            // 문제 등록
            quizService.createQuiz(quiz);

            response.put("success", true);
            response.put("message", "문제가 성공적으로 등록되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid quiz data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error creating quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 등록 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 문제 수정 처리 (AJAX) - 향후 구현
     */
    @PostMapping("/{quizId}")
    @ResponseBody
    public HashMap<String, Object> updateQuiz(@PathVariable("quizId") Long quizId, @RequestBody Quiz quiz) {
        log.debug("Updating quiz. ID: {}, Data: {}", quizId, quiz);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 수정자 정보 설정
            quiz.setQuizId(quizId);
            quiz.setLastUpdateId(auth.getName());

            // 문제 수정
            quizService.updateQuiz(quiz);

            response.put("success", true);
            response.put("message", "문제가 성공적으로 수정되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid quiz data: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error updating quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 수정 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 문제 삭제 처리 (AJAX) - 향후 구현
     */
    @DeleteMapping("/{quizId}")
    @ResponseBody
    public HashMap<String, Object> deleteQuiz(@PathVariable("quizId") Long quizId) {
        log.debug("Deleting quiz. ID: {}", quizId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 문제 삭제
            quizService.deleteQuiz(quizId, auth.getName());

            response.put("success", true);
            response.put("message", "문제가 성공적으로 삭제되었습니다.");

        } catch (IllegalArgumentException e) {
            log.warn("Invalid quiz ID: {}", e.getMessage());
            response.put("success", false);
            response.put("message", e.getMessage());

        } catch (Exception e) {
            log.error("Error deleting quiz: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "문제 삭제 중 오류가 발생했습니다.");
        }

        return response;
    }

    /**
     * 문제 상태 토글 (AJAX)
     */
    @PostMapping("/{quizId}/toggle-status")
    @ResponseBody
    public HashMap<String, Object> toggleQuizStatus(@PathVariable("quizId") Long quizId) {
        log.debug("Toggling quiz status. ID: {}", quizId);

        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        HashMap<String, Object> response = new HashMap<>();

        try {
            // 현재 문제 정보 조회
            Quiz currentQuiz = quizService.findQuizById(quizId);

            // 상태 토글
            String newStatus = "ACTIVE".equals(currentQuiz.getStatus()) ? "INACTIVE" : "ACTIVE";

            // 상태 업데이트
            quizService.updateQuizStatus(quizId, newStatus, auth.getName());

            response.put("success", true);
            response.put("newStatus", newStatus);
            response.put("statusDisplay", "ACTIVE".equals(newStatus) ? "활성" : "비활성");
            response.put("message", "문제 상태가 성공적으로 변경되었습니다.");

        } catch (Exception e) {
            log.error("Error toggling quiz status: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", e.getMessage());
        }

        return response;
    }
}
