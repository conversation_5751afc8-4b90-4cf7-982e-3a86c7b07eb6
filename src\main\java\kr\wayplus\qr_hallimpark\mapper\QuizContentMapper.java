package kr.wayplus.qr_hallimpark.mapper;

import kr.wayplus.qr_hallimpark.model.QuizContent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 문제 콘텐츠 매퍼
 * - quiz_content 테이블 CRUD 처리
 * - 다국어 콘텐츠 관리
 */
@Mapper
@Repository
public interface QuizContentMapper {
    
    /**
     * 문제 ID로 모든 언어의 콘텐츠 조회
     * @param quizId 문제 ID
     * @return 콘텐츠 목록
     */
    List<QuizContent> selectQuizContentsByQuizId(Long quizId);
    
    /**
     * 문제 ID와 언어 코드로 콘텐츠 조회
     * @param quizId 문제 ID
     * @param langCode 언어 코드
     * @return 콘텐츠 정보
     */
    QuizContent selectQuizContentByQuizIdAndLangCode(@Param("quizId") Long quizId, @Param("langCode") String langCode);
    
    /**
     * 콘텐츠 등록
     * @param quizContent 콘텐츠 정보
     * @return 등록된 행 수
     */
    int insertQuizContent(QuizContent quizContent);
    
    /**
     * 콘텐츠 수정
     * @param quizContent 콘텐츠 정보
     * @return 수정된 행 수
     */
    int updateQuizContent(QuizContent quizContent);

    /**
     * 문제 ID와 언어 코드로 콘텐츠 수정
     * @param quizContent 콘텐츠 정보
     * @return 수정된 행 수
     */
    int updateQuizContentByQuizIdAndLangCode(QuizContent quizContent);
    
    /**
     * 콘텐츠 삭제 (논리 삭제)
     * @param quizContentId 콘텐츠 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteQuizContent(@Param("quizContentId") Long quizContentId, @Param("deleteId") String deleteId);
    
    /**
     * 문제 ID로 모든 콘텐츠 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteQuizContentsByQuizId(@Param("quizId") Long quizId, @Param("deleteId") String deleteId);
    
    /**
     * 문제 ID와 언어 코드로 콘텐츠 존재 여부 확인
     * @param quizId 문제 ID
     * @param langCode 언어 코드
     * @return 존재하는 콘텐츠 개수
     */
    int countQuizContentByQuizIdAndLangCode(@Param("quizId") Long quizId, @Param("langCode") String langCode);
    
    /**
     * 문제 질문 내용으로 중복 체크
     * @param question 문제 질문
     * @param langCode 언어 코드
     * @param excludeQuizId 제외할 문제 ID (수정 시 사용)
     * @return 중복된 콘텐츠 개수
     */
    int countDuplicateQuestionByLangCode(@Param("question") String question,
                                        @Param("langCode") String langCode,
                                        @Param("excludeQuizId") Long excludeQuizId);

    /**
     * 문제 ID와 언어 코드로 콘텐츠 활성화
     * @param quizId 문제 ID
     * @param langCode 언어 코드
     * @param lastUpdateId 수정자 ID
     * @return 수정된 행 수
     */
    int activateQuizContentByQuizIdAndLangCode(@Param("quizId") Long quizId,
                                              @Param("langCode") String langCode,
                                              @Param("lastUpdateId") String lastUpdateId);

    /**
     * 문제 ID와 언어 코드로 콘텐츠 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param langCode 언어 코드
     * @param deleteId 삭제자 ID
     * @return 삭제된 행 수
     */
    int deleteQuizContentByQuizIdAndLangCode(@Param("quizId") Long quizId,
                                           @Param("langCode") String langCode,
                                           @Param("deleteId") String deleteId);
}
