package kr.wayplus.qr_hallimpark.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * 문제 모델
 * - quiz_master와 quiz_content 테이블을 조인한 정보
 * - 문제 관리 페이지에서 사용되는 통합 모델
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Quiz {

    // ========== quiz_master 테이블 필드 ==========
    
    /**
     * 문제 고유 ID
     */
    private Long quizId;

    /**
     * 카테고리 ID (FK)
     */
    private Long categoryId;

    /**
     * 문제 제목 (관리용)
     */
    private String title;

    /**
     * 문제 유형
     * - MCQ: 객관식
     * - OX: OX 퀴즈
     * - ORDER: 순서 정렬
     * - IMAGE_VOICE: 이미지/음성 인식
     * - PUZZLE_MEMORY: 퍼즐/기억력 게임
     */
    private String quizType;

    /**
     * 문제 상태
     * - ACTIVE: 활성
     * - INACTIVE: 비활성
     */
    private String status;

    /**
     * 난이도 (1~5)
     */
    private Integer difficultyLevel;

    /**
     * 정답 (JSON, 텍스트 등)
     */
    private String correctAnswer;

    /**
     * 문제 이미지 경로
     */
    private String imageUrl;

    /**
     * 힌트 내용
     */
    private String hint;

    /**
     * 생성자 (user_email)
     */
    private String createId;

    /**
     * 생성일시
     */
    private LocalDateTime createDate;

    /**
     * 최종수정자 (user_email)
     */
    private String lastUpdateId;

    /**
     * 최종수정일시
     */
    private LocalDateTime lastUpdateDate;

    /**
     * 삭제여부
     */
    private String deleteYn;

    /**
     * 삭제자 (user_email)
     */
    private String deleteId;

    /**
     * 삭제일시
     */
    private LocalDateTime deleteDate;

    // ========== quiz_content 테이블 필드 (기본 언어: 한국어) ==========

    /**
     * 문제 질문 내용 (한국어)
     */
    private String question;

    /**
     * 문제 보기 (JSON 형태, 객관식 등에서 사용)
     */
    private String options;

    // ========== 조인 및 계산 필드 ==========

    /**
     * 카테고리명
     */
    private String categoryName;

    /**
     * 부모 카테고리명 (서브카테고리인 경우)
     */
    private String parentCategoryName;

    /**
     * 카테고리 전체 경로 (부모 > 자식)
     */
    private String categoryPath;

    /**
     * 상태 표시용 (한국어)
     * - 계산된 필드 (DB 저장 X)
     */
    private String statusDisplay;

    /**
     * 게임 유형 (한국어 표시용)
     */
    private String gameTypeDisplay;

    /**
     * 다국어 설정 현황
     * - 지원하는 언어 목록 (예: "한국어, 영어, 일본어")
     */
    private String languageSupport;

    /**
     * QR 연동 개수
     * - 이 문제와 연결된 QR 코드의 개수
     */
    private Integer qrMappingCount;

    /**
     * 다국어 콘텐츠 목록
     * - 폼에서 전송되는 다국어 데이터 처리용
     */
    private List<QuizContent> languages;

    // ========== 유틸리티 메서드 ==========

    /**
     * 활성 상태인지 확인
     * @return 활성 상태 여부
     */
    public boolean isActive() {
        return "N".equals(this.deleteYn);
    }

    /**
     * 게임 유형을 한국어로 변환
     * @return 한국어 게임 유형
     */
    public String getGameTypeDisplay() {
        if (this.quizType == null) {
            return "미정";
        }
        
        switch (this.quizType) {
            case "MCQ":
                return "객관식";
            case "OX":
                return "OX 퀴즈";
            case "ORDER":
                return "순서 정렬";
            case "IMAGE_VOICE":
                return "이미지/음성 인식";
            case "PUZZLE_MEMORY":
                return "퍼즐/기억력 게임";
            default:
                return this.quizType;
        }
    }

    /**
     * 상태를 한국어로 변환
     * @return 한국어 상태
     */
    public String getStatusDisplay() {
        if ("ACTIVE".equals(this.status)) {
            return "활성";
        } else if ("INACTIVE".equals(this.status)) {
            return "비활성";
        } else {
            // 삭제 여부로 판단 (기존 로직 유지)
            return isActive() ? "활성" : "비활성";
        }
    }

    /**
     * 서브카테고리 여부 확인
     * @return 서브카테고리 여부
     */
    public boolean hasParentCategory() {
        return this.parentCategoryName != null && !this.parentCategoryName.trim().isEmpty();
    }

    /**
     * QR 연동 여부 확인
     * @return QR 연동 여부
     */
    public boolean hasQrMapping() {
        return this.qrMappingCount != null && this.qrMappingCount > 0;
    }
}
