package kr.wayplus.qr_hallimpark.service;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.common.service.impl.BaseAdminListService;
import kr.wayplus.qr_hallimpark.mapper.QuizMapper;
import kr.wayplus.qr_hallimpark.mapper.QuizContentMapper;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Quiz;
import kr.wayplus.qr_hallimpark.model.QuizCategory;
import kr.wayplus.qr_hallimpark.model.QuizContent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * 문제 서비스
 * - 문제 CRUD 비즈니스 로직 처리
 * - 공통 리스트 기능 지원
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class QuizService extends BaseAdminListService<Quiz> {
    private final QuizMapper quizMapper;
    private final QuizContentMapper quizContentMapper;
    private final QuizCategoryService quizCategoryService;

    // ========== 공통 리스트 기능 구현 ==========

    @Override
    protected AdminListMapper<Quiz> getMapper() {
        return quizMapper;
    }

    @Override
    protected String getTableName() {
        return "quiz_master";
    }

    @Override
    protected String getDefaultSortField() {
        return "quiz_id"; // 최신 생성 순으로 정렬 (ID가 높을수록 최신)
    }

    @Override
    public AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField("quiz_id")
                .sortDirection("DESC") // 내림차순으로 최신 항목이 위로
                .baseCondition("qm.delete_yn = 'N' AND cat.delete_yn = 'N'")
                .tableName(getTableName())
                .build();
    }

    @Override
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 허용된 검색 필드 검증
        List<String> allowedSearchFields = Arrays.asList("question", "category_name", "quiz_type");
        validateSearchFields(searchCondition, allowedSearchFields);

        // 허용된 정렬 필드 검증
        List<String> allowedSortFields = Arrays.asList("quiz_id", "question", "category_name", "quiz_type", "create_date", "last_update_date");
        validateSortField(searchCondition, allowedSortFields);
    }

    // ========== 문제 관련 비즈니스 로직 ==========
    
    /**
     * 모든 문제 목록 조회
     * @return 문제 목록
     */
    public List<Quiz> findAllQuizzes() {
        log.debug("Finding all quizzes");
        return quizMapper.selectQuizList();
    }
    
    /**
     * 문제 ID로 문제 조회
     * @param quizId 문제 ID
     * @return 문제 정보
     */
    public Quiz findQuizById(Long quizId) {
        log.debug("Finding quiz by ID: {}", quizId);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        Quiz quiz = quizMapper.selectQuizById(quizId);
        if (quiz == null) {
            throw new IllegalArgumentException("존재하지 않는 문제입니다. ID: " + quizId);
        }

        return quiz;
    }

    /**
     * 문제 ID로 문제 조회 (다국어 콘텐츠 포함)
     * @param quizId 문제 ID
     * @return 문제 정보 (다국어 콘텐츠 포함)
     */
    public Quiz findQuizByIdWithContents(Long quizId) {
        log.debug("Finding quiz with contents by ID: {}", quizId);

        // 기본 문제 정보 조회
        Quiz quiz = findQuizById(quizId);

        // 다국어 콘텐츠 조회
        List<QuizContent> contents = quizContentMapper.selectQuizContentsByQuizId(quizId);
        quiz.setLanguages(contents);

        return quiz;
    }

    /**
     * 특정 카테고리의 문제 목록 조회
     * @param categoryId 카테고리 ID
     * @return 문제 목록
     */
    public List<Quiz> findQuizzesByCategoryId(Long categoryId) {
        log.debug("Finding quizzes by category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.selectQuizListByCategoryId(categoryId);
    }

    /**
     * 특정 카테고리와 그 하위 카테고리의 문제 목록 조회
     * @param categoryId 상위 카테고리 ID
     * @return 문제 목록
     */
    public List<Quiz> findQuizzesWithChildCategories(Long categoryId) {
        log.debug("Finding quizzes with child categories for category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.selectQuizListByCategoryIdWithChildren(categoryId);
    }

    /**
     * 최상위 카테고리 목록 조회 (문제 관리용)
     * @return 최상위 카테고리 목록
     */
    public List<QuizCategory> findRootCategoriesForQuizManagement() {
        log.debug("Finding root categories for quiz management");
        return quizCategoryService.findRootCategories();
    }

    /**
     * 하위 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 하위 카테고리 목록
     */
    public List<QuizCategory> findChildCategories(Long parentId) {
        log.debug("Finding child categories for parent ID: {}", parentId);
        return quizCategoryService.findChildCategories(parentId);
    }

    /**
     * 카테고리별 문제 개수 조회
     * @param categoryId 카테고리 ID
     * @return 문제 개수
     */
    public int countQuizzesByCategoryId(Long categoryId) {
        log.debug("Counting quizzes by category ID: {}", categoryId);
        
        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID는 필수입니다.");
        }
        
        return quizMapper.countQuizByCategoryId(categoryId);
    }

    // ========== 문제 CRUD 메서드 (향후 구현용) ==========
    
    /**
     * 문제 등록
     * @param quiz 문제 정보
     * @return 등록된 문제 정보
     */
    @Transactional
    public Quiz createQuiz(Quiz quiz) {
        log.debug("Creating quiz: {}", quiz);

        validateQuiz(quiz);

        // 한국어 문제명 중복 체크
        if (quiz.getLanguages() != null && !quiz.getLanguages().isEmpty()) {
            QuizContent koContent = quiz.getLanguages().stream()
                .filter(content -> "ko".equals(content.getLangCode()))
                .findFirst()
                .orElse(null);

            if (koContent != null && isDuplicateQuestion(koContent.getQuestion(), null)) {
                throw new IllegalArgumentException("이미 존재하는 문제입니다: " + koContent.getQuestion());
            }
        }

        // 1. quiz_master 테이블에 문제 등록
        int result = quizMapper.insertQuiz(quiz);
        if (result != 1) {
            throw new RuntimeException("문제 등록에 실패했습니다.");
        }

        // 2. quiz_content 테이블에 다국어 콘텐츠 등록
        if (quiz.getLanguages() != null && !quiz.getLanguages().isEmpty()) {
            for (QuizContent content : quiz.getLanguages()) {
                content.setQuizId(quiz.getQuizId());
                content.setCreateId(quiz.getCreateId());
                content.setDeleteYn("N");

                int contentResult = quizContentMapper.insertQuizContent(content);
                if (contentResult != 1) {
                    throw new RuntimeException("다국어 콘텐츠 등록에 실패했습니다. 언어: " + content.getLangCode());
                }
            }
        }

        log.info("Quiz created successfully. ID: {}", quiz.getQuizId());
        return quiz;
    }

    /**
     * 문제 수정
     * @param quiz 문제 정보
     * @return 수정된 문제 정보
     */
    @Transactional
    public Quiz updateQuiz(Quiz quiz) {
        log.debug("Updating quiz: {}", quiz);

        validateQuiz(quiz);

        // 기존 문제 존재 여부 확인
        findQuizById(quiz.getQuizId());

        // 한국어 문제명 중복 체크 (자기 자신 제외)
        if (quiz.getLanguages() != null && !quiz.getLanguages().isEmpty()) {
            QuizContent koContent = quiz.getLanguages().stream()
                .filter(content -> "ko".equals(content.getLangCode()))
                .findFirst()
                .orElse(null);

            if (koContent != null && isDuplicateQuestion(koContent.getQuestion(), quiz.getQuizId())) {
                throw new IllegalArgumentException("이미 존재하는 문제입니다: " + koContent.getQuestion());
            }
        }

        // 1. quiz_master 테이블 수정
        int result = quizMapper.updateQuiz(quiz);
        if (result != 1) {
            throw new RuntimeException("문제 수정에 실패했습니다.");
        }

        // 2. quiz_content 테이블 수정 (기존 콘텐츠 업데이트 또는 신규 등록)
        if (quiz.getLanguages() != null && !quiz.getLanguages().isEmpty()) {
            for (QuizContent content : quiz.getLanguages()) {
                content.setQuizId(quiz.getQuizId());
                content.setLastUpdateId(quiz.getLastUpdateId());
                content.setDeleteYn("N");

                // 기존 콘텐츠 존재 여부 확인
                int existingCount = quizContentMapper.countQuizContentByQuizIdAndLangCode(
                    quiz.getQuizId(), content.getLangCode());

                if (existingCount > 0) {
                    // 기존 콘텐츠 업데이트
                    int contentResult = quizContentMapper.updateQuizContentByQuizIdAndLangCode(content);
                    if (contentResult != 1) {
                        throw new RuntimeException("다국어 콘텐츠 수정에 실패했습니다. 언어: " + content.getLangCode());
                    }
                } else {
                    // 새 콘텐츠 등록
                    content.setCreateId(quiz.getLastUpdateId());
                    int contentResult = quizContentMapper.insertQuizContent(content);
                    if (contentResult != 1) {
                        throw new RuntimeException("다국어 콘텐츠 등록에 실패했습니다. 언어: " + content.getLangCode());
                    }
                }
            }
        }

        log.info("Quiz updated successfully. ID: {}", quiz.getQuizId());
        return quiz;
    }

    /**
     * 문제 상태만 업데이트
     * @param quizId 문제 ID
     * @param status 새로운 상태
     * @param lastUpdateId 수정자 ID
     * @return 수정된 문제 정보
     */
    @Transactional
    public Quiz updateQuizStatus(Long quizId, String status, String lastUpdateId) {
        log.debug("Updating quiz status. ID: {}, Status: {}", quizId, status);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (status == null || status.trim().isEmpty()) {
            throw new IllegalArgumentException("상태는 필수입니다.");
        }

        if (lastUpdateId == null || lastUpdateId.trim().isEmpty()) {
            throw new IllegalArgumentException("수정자 ID는 필수입니다.");
        }

        // 기존 문제 존재 여부 확인
        Quiz existingQuiz = findQuizById(quizId);

        // quiz_master 테이블 상태만 수정
        int result = quizMapper.updateQuizStatus(quizId, status, lastUpdateId);
        if (result != 1) {
            throw new RuntimeException("문제 상태 수정에 실패했습니다.");
        }

        // 업데이트된 정보로 Quiz 객체 반환
        existingQuiz.setStatus(status);
        existingQuiz.setLastUpdateId(lastUpdateId);

        log.info("Quiz status updated successfully. ID: {}, Status: {}", quizId, status);
        return existingQuiz;
    }

    /**
     * 문제 삭제 (논리 삭제)
     * @param quizId 문제 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteQuiz(Long quizId, String deleteId) {
        log.debug("Deleting quiz. ID: {}, deleteId: {}", quizId, deleteId);

        if (quizId == null) {
            throw new IllegalArgumentException("문제 ID는 필수입니다.");
        }

        if (deleteId == null || deleteId.trim().isEmpty()) {
            throw new IllegalArgumentException("삭제자 ID는 필수입니다.");
        }

        // 기존 문제 존재 여부 확인
        findQuizById(quizId);

        // 1. quiz_content 테이블 삭제 (논리 삭제)
        quizContentMapper.deleteQuizContentsByQuizId(quizId, deleteId);

        // 2. quiz_master 테이블 삭제 (논리 삭제)
        int result = quizMapper.deleteQuiz(quizId, deleteId);
        if (result != 1) {
            throw new RuntimeException("문제 삭제에 실패했습니다.");
        }

        log.info("Quiz deleted successfully. ID: {}", quizId);
    }

    // ========== 유효성 검사 및 유틸리티 메서드 ==========

    /**
     * 문제 정보 유효성 검사
     * @param quiz 문제 정보
     */
    private void validateQuiz(Quiz quiz) {
        if (quiz == null) {
            throw new IllegalArgumentException("문제 정보는 필수입니다.");
        }

        if (quiz.getTitle() == null || quiz.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("문제 제목은 필수입니다.");
        }

        if (quiz.getCategoryId() == null) {
            throw new IllegalArgumentException("카테고리는 필수입니다.");
        }

        if (quiz.getQuizType() == null || quiz.getQuizType().trim().isEmpty()) {
            throw new IllegalArgumentException("문제 유형은 필수입니다.");
        }
        
        // 정답 검증은 향후 구현 예정
        // if (quiz.getCorrectAnswer() == null || quiz.getCorrectAnswer().trim().isEmpty()) {
        //     throw new IllegalArgumentException("정답은 필수입니다.");
        // }
    }

    /**
     * 문제명 중복 체크
     * @param question 문제 내용
     * @param excludeQuizId 제외할 문제 ID (수정 시 사용)
     * @return 중복 여부
     */
    private boolean isDuplicateQuestion(String question, Long excludeQuizId) {
        if (question == null || question.trim().isEmpty()) {
            return false;
        }
        
        int count = quizMapper.countDuplicateQuestion(question.trim(), excludeQuizId);
        return count > 0;
    }
}
